# Contributing to Awesome Claude Subagents

Thank you for your interest in contributing to this collection!

## 🤝 How to Contribute

### Adding a New Subagent

1. **Choose the right category** - Place your subagent in the most appropriate category folder
2. **Test your subagent** - Ensure it works with Claude Code
3. **Submit a PR** - Include a clear description of the subagent's purpose

### Subagent Requirements

Each subagent should include:
- Clear role definition
- List of expertise areas
- Required MCP tools (if any)
- Communication protocol examples
- Core capabilities
- Example usage scenarios
- Best practices

### Code of Conduct

- Be respectful and inclusive
- Provide constructive feedback
- Test contributions before submitting
- Follow the existing format and structure

### Pull Request Process

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/new-subagent`)
3. Add your subagent following the template
4. Update the category README if needed
5. Submit a pull request with a clear description

### Quality Guidelines

- Subagents should be production-ready
- Include clear documentation
- Provide practical examples
- Ensure compatibility with Claude Code

## 📝 License

By contributing, you agree that your contributions will be licensed under the MIT License.