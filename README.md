# 🚀 Awesome Claude Subagents

A powerful collection of specialized AI development agents designed to supercharge your Claude Code experience. Each agent is a domain expert with deep technical knowledge and strict operational boundaries, enabling efficient handling of specific technology stacks and development tasks.

## ✨ What Makes This Special

- **🎯 Project-Specific Intelligence**: The `/initx` command analyzes your codebase and creates custom agents tailored to your exact tech stack and business logic
- **🏗️ 28+ Professional Agents**: Complete coverage of modern development - from backend APIs to mobile apps, from security analysis to DevOps automation
- **🔒 Smart Boundaries**: Each agent has strict ALLOWED/FORBIDDEN constraints to prevent scope creep and ensure precise execution
- **⚡ Performance Optimized**: 3x speed improvement for simple tasks, 2x reliability for complex coordination
- **🔄 Dependency-Aware**: Intelligent dependency detection with phase-based execution strategies

## 🚀 Quick Start

### 1. Installation
```bash
# Clone the repository
git clone https://github.com/Toskysun/sub-agents.git
cd sub-agents

# Copy to Claude Code directory
# Linux/Mac
cp -r * ~/.claude/

# Windows
copy * %USERPROFILE%\.claude\

# Restart Claude Code
```

### 2. Create Your Custom AI Team
```bash
# Navigate to your project
cd your_project

# Analyze and create project-specific agents
/initx

# Preview mode (no file creation)
/initx --preview

# Focus on specific domains
/initx --focus=security
/initx --template=mobile
```

### 3. Start Building
```bash
# Use general agents
@backend-developer "Create a FastAPI user authentication endpoint"
@react-developer "Build a responsive dashboard component"

# Use your custom project agents (after /initx)
@vue-ecommerce-developer "Optimize the shopping cart performance"
@payment-integration-specialist "Add Stripe webhook handling"
```

## 🎯 The `/initx` Magic

The most powerful feature - automatically creates custom agents for your specific project:

**What it does:**
- 🔍 Analyzes your project structure, dependencies, and patterns
- 🧠 Detects technology stack (Vue, React, FastAPI, Spring Boot, etc.)
- 🎨 Creates specialized agents with deep knowledge of your codebase
- 📝 Updates your project's `CLAUDE.md` with the new AI team

**Example generated agents:**
- `vue-ecommerce-developer` - E-commerce Vue.js specialist
- `postgres-inventory-architect` - Database-specific data expert
- `aws-deployment-specialist` - Your cloud platform expert
- `stripe-payment-integrator` - Payment system specialist

**Benefits:**
- **10x Faster Development**: Agents understand your specific business logic
- **Zero Learning Curve**: Pre-configured with your project patterns
- **Smart Collaboration**: Knows your unique integration points

## 📁 Agent Categories

### 🏗️ Core Development
- **backend-developer**: Multi-stack backend (FastAPI, Spring Boot, Node.js)
- **frontend-developer**: Modern frontend (React, Vue, Angular)
- **fullstack-developer**: End-to-end application development
- **mobile-developer**: Cross-platform mobile (React Native, Flutter)

### 🔧 Language Specialists
- **python-pro**: FastAPI, Django, data science, ML integration
- **go-architect**: High-performance services, concurrency, cloud-native
- **rust-architect**: Systems programming, performance-critical applications
- **java-developer**: Enterprise applications, Spring ecosystem
- **react-developer**: React ecosystem, hooks, state management
- **vue-developer**: Vue.js, Nuxt.js, composition API

### ☁️ Infrastructure & DevOps
- **devops-engineer**: CI/CD, containerization, orchestration
- **infrastructure-developer**: Cloud architecture, Infrastructure as Code
- **cloud-architect**: Multi-cloud strategies and migration

### 🔍 Quality & Security
- **code-review-expert**: Code quality and best practices
- **security-analyst**: Vulnerability assessment and mitigation
- **qa-engineer**: Testing strategy and quality analysis
- **test-expert**: Testing framework design and strategy

### 📱 Specialized Domains
- **android-developer**: Native Android development
- **android-hooking-expert**: Frida, dynamic analysis, runtime manipulation
- **reverse-engineer**: Static analysis, decompilation, vulnerability research
- **malware-analyst**: Threat detection, behavioral analysis

### 🎨 Design & Experience
- **ui-designer**: Interface design and prototyping
- **ux-researcher**: User experience and usability
- **design-system-architect**: Component libraries and design tokens

### 📊 Data & AI
- **data-scientist**: ML models and data analysis
- **ml-engineer**: Production ML systems
- **analytics-engineer**: Data pipelines and metrics

### 🔧 MCP Protocol Specialists
- **fastmcp-specialist**: FastMCP server development
- **mcp-deployment-specialist**: MCP production deployment
- **mcp-security-auditor**: MCP security and compliance
- **mcp-performance-optimizer**: MCP optimization and scaling

## 🎛️ Advanced Features

### Model Selection
```bash
/initx --model=inherit   # All agents use parent session model (cost control)
/initx --model=sonnet    # Balanced performance
/initx --model=opus      # Maximum reasoning capability
/initx --model=haiku     # Fastest response time
```

### Specialized Templates
```bash
/initx --template=mobile     # Mobile app development focus
/initx --template=ecommerce  # E-commerce platform focus
/initx --template=enterprise # Enterprise application focus
```

### Smart Coordination
The `task-dispatch-director` agent automatically coordinates multiple specialists for complex tasks:

```bash
@task-dispatch-director "Build a complete e-commerce checkout flow"
# Automatically coordinates: backend-developer, frontend-developer, qa-engineer
```

## 📂 Project Structure

```
awesome-claude-subagents/
├── categories/                    # Organized by domain expertise
│   ├── 01-core-development/      # Backend, frontend, fullstack
│   ├── 02-language-specialists/  # Python, Go, Rust, Java experts
│   ├── 03-infrastructure/        # DevOps, cloud, deployment
│   ├── 04-quality-security/      # Testing, security, code review
│   ├── 05-data-ai/              # ML, data science, analytics
│   ├── 06-developer-experience/ # DX, tooling, documentation
│   ├── 07-specialized-domains/   # Mobile, game dev, embedded
│   ├── 08-business-product/      # PM, design, strategy
│   ├── 09-meta-orchestration/    # Task coordination, planning
│   └── 10-research-analysis/     # Research, technical writing
├── pro sub agents/               # Premium specialized agents
├── MCP Developer subagents/      # MCP protocol specialists
└── commands/                     # Custom commands (initx)
```

## 🔒 Agent Boundaries

Each agent has strict operational boundaries to ensure focused, high-quality output:

**Implementation Agents** (Can write code):
- backend-developer, frontend-developer, python-pro, etc.

**Advisory Agents** (Analysis and guidance only):
- qa-engineer, test-expert (cannot write production code)
- task-dispatch-director (coordination only, never calls itself)

## 🌟 Real-World Examples

### E-commerce Project
After running `/initx` on an e-commerce project:
```bash
@vue-ecommerce-developer "Add product filtering with price ranges"
@payment-integration-specialist "Implement subscription billing"
@postgres-inventory-architect "Optimize product search queries"
```

### Mobile App Project
```bash
@react-native-specialist "Add biometric authentication"
@mobile-ui-designer "Design onboarding flow"
@app-store-optimizer "Prepare release metadata"
```

### Enterprise Backend
```bash
@spring-microservices-architect "Design user service API"
@kubernetes-deployment-expert "Set up production environment"
@security-compliance-auditor "Review authentication flow"
```

## 🎯 Key Features

- **Enhanced Task Assessment**: 6-level (0-5) complexity evaluation with ultra-intelligent analysis
- **Agent Boundary System**: ALLOWED/FORBIDDEN modes prevent scope creep
- **Multi-Stack Support**: Vue.js, React, Go, Rust, Python, Android, Java, security analysis
- **Production-Ready**: Battle-tested configurations optimized for real-world development
- **Performance Optimized**: 3x speed improvement for simple tasks, 2x reliability for complex coordination

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for guidelines on:
- Adding new subagents
- Improving existing agents
- Testing and quality standards
- Pull request process

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 Acknowledgments

- Claude Code team for the amazing development environment
- All contributors who have helped build this collection
- The open-source community for inspiration and best practices

---

**Ready to supercharge your development workflow?** Start with `/initx` to create your custom AI development team! 🚀